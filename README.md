# Money Mouthy

## Table of Contents

- [Project Overview](#project-overview)
- [Features](#features)
- [Getting Started](#getting-started)
- [Project Structure](#project-structure)
- [Contributing](#contributing)
- [License](#license)

## Project Overview

Money Mouthy is a revolutionary microblogging platform where your voice has minimum value. Share your opinions, engage with others, and get rewarded for meaningful content across six dynamic categories: News, Politics, Sports, Entertainment, Sex, and Religion.

## Features

### User Features

- **Monetized Posts**: Share your thoughts for a minimum of \$0.05
- **Top Rankings**: Highest paid posts stay at the top for 24 hours
- **Real-time Updates**: Live post and wallet monitoring
- **Secure Transactions**: End-to-end encryption and secure payment processing
- **Community Engagement**: Follow, connect, and engage with others
- **Content Creation**: Share text, images, videos, and links
- **Influencer Potential**: Build your following and grow your influence

### Admin Features

- **User Management**: Create, edit, and block users
- **Wallet Management**: View and manage user wallets and transactions
- **Analytics**: Real-time analytics and reporting
- **Admin Management**: Create and manage other admin accounts
- **System Administration**: Full system administration access

## Getting Started

### Prerequisites

- Flutter SDK (version 3.10.6)
- Firebase account
- Stripe account
- Android/iOS development environment (for testing)

### Installation

1. Clone the repository
2. Install dependencies: `flutter pub get`
3. Set up Firebase and Stripe (see documentation)
4. Run the app: `flutter run`

## Project Structure

The project is structured as follows:

```
money_mouthy_two/
├── android/
├── ios/
├── lib/
│   ├── admin/         # Admin panel
│   ├── models/        # Data models
│   ├── screens/       # UI screens
│   ├── services/      # Business logic and API calls
│   ├── utils/         # Utility functions
│   └── main.dart      # Main entry point
├── test/              # Unit and widget tests
├── pubspec.yaml       # Dependencies
└── README.md          # Project documentation
```

## Contributing

Contributions are welcome! Please read the [contributing guidelines](CONTRIBUTING.md) for more information.

## License

This project is licensed under the MIT License.
