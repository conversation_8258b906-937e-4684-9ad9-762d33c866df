import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/post_controller.dart';
import 'package:money_mouthy_two/firebase_options.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:money_mouthy_two/controllers/category_controller.dart';
import 'package:money_mouthy_two/controllers/profile_controller.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/user_cache_service.dart';
import 'package:money_mouthy_two/services/deep_link_handler.dart';
import 'package:money_mouthy_two/services/web_deep_link_handler.dart';
import 'screens/sign_up.dart';
import 'screens/login.dart';
import 'screens/create_account.dart';
import 'screens/create_profile.dart';
import 'screens/category_selection.dart';
import 'screens/choose_username.dart';
import 'screens/otp_verification.dart';
import 'screens/main_navigation_screen.dart';
import 'screens/create_post.dart';
import 'screens/post_feed.dart';
import 'screens/categories_ranking.dart';
import 'screens/wallet_screen.dart';
import 'screens/landing_page.dart';
import 'screens/support_page.dart';
import 'screens/privacy_policy_page.dart';
import 'screens/terms_of_service_page.dart';
import 'screens/contact_page.dart';
import 'screens/about_screen.dart';
import 'screens/post_detail_screen.dart';

import 'package:firebase_core/firebase_core.dart';
import 'screens/search_screen.dart';
import 'screens/chat_list_screen.dart';
import 'screens/edit_profile_screen.dart';
import 'screens/connect_screen.dart';
import 'admin/routes/admin_routes.dart';
import 'middleware/auth_middleware.dart';

void main() async {
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  //initialize firebase
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
    debugPrint('Firebase initialized successfully');

    // Configure Firestore settings for web
    if (kIsWeb) {
      try {
        FirebaseFirestore.instance.settings = const Settings(
          persistenceEnabled: true,
        );
        debugPrint('Firestore web persistence enabled');
      } catch (e) {
        debugPrint('Failed to enable Firestore web persistence: $e');
        // Fallback to no persistence
        try {
          FirebaseFirestore.instance.settings = const Settings(
            persistenceEnabled: false,
          );
          debugPrint('Firestore web persistence disabled as fallback');
        } catch (fallbackError) {
          debugPrint('Firestore settings fallback failed: $fallbackError');
        }
      }
    }
  } catch (e) {
    debugPrint('Firebase initialization error: $e');
    // Continue with app initialization even if Firebase fails
  }

  // Initialize Stripe (mobile and web)
  // Stripe.publishableKey = kDebugMode
  //     ? 'pk_test_51RLe2ZIg599FILjxQMFFClRwOLRHHaNXr0FKJEPLeUEPflIGEgBOkaZ7hk5kTWE5p9dPwl4X8TuMbemSyiVdmWiN007oGUMK2a'
  //     : 'pk_live_51RUa8mGB1vrtnQ21KAAri5UGtp0e3xfQAbIT2dLMDQXbDMkA5zghmdKJGJECYevy6zlLmoYSd2y6dcd9mlptw6Pd00lQRClkRX';
  Stripe.publishableKey =
      'pk_live_51RUa8mGB1vrtnQ21KAAri5UGtp0e3xfQAbIT2dLMDQXbDMkA5zghmdKJGJECYevy6zlLmoYSd2y6dcd9mlptw6Pd00lQRClkRX';

  // Web-specific Stripe setup
  if (kIsWeb) {
    Stripe.merchantIdentifier = 'Money Mouthy';
  }

  // Initialize GetX controllers
  _initializeControllers();

  // Initialize deep link handler
  await _initializeDeepLinkHandler();

  // Initialize EasyLocalization only for web
  if (kIsWeb) {
    await EasyLocalization.ensureInitialized();
    runApp(
      EasyLocalization(
        supportedLocales: const [
          Locale('en'),
          Locale('es'),
          Locale('fr'),
          Locale('de'),
          Locale('zh'),
          Locale('ja'),
          Locale('ar'),
        ],
        path: 'assets/translations',
        fallbackLocale: const Locale('en'),
        child: const MyApp(),
      ),
    );
  } else {
    runApp(const MyApp());
  }
}

void _initializeControllers() {
  // Initialize only essential controllers before authentication
  // Other controllers will be initialized in MainNavigationScreen after authentication
  Get.put(ProfileController(), permanent: true); // Needed for auth flow
  Get.put(UserCacheService(), permanent: true); // Needed for user data caching

  debugPrint(
      'Main: Essential controllers initialized (ProfileController, UserCacheService)');
  debugPrint(
      'Main: Other controllers will be initialized after authentication');
}

Future<void> _initializeDeepLinkHandler() async {
  try {
    if (kIsWeb) {
      // Use web-specific deep link handler
      final webDeepLinkHandler = WebDeepLinkHandler();
      webDeepLinkHandler.initialize();
    } else {
      // Use mobile deep link handler
      final deepLinkHandler = DeepLinkHandler();
      await deepLinkHandler.initialize();
    }
  } catch (e) {
    // Silently handle initialization errors
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // Generate routes for deep linking - simplified since we handle deep links after screens load
  Route<dynamic>? _generateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';
    debugPrint('_generateRoute called with: $routeName');

    // Let the regular routes and GetX handle routing
    // Deep links are handled after screens are loaded
    return null;
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Money Mouthy',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
      onGenerateRoute: _generateRoute,
      initialRoute: '/',
      getPages: [
        GetPage(
          name: '/',
          page: () => const AuthWrapper(),
        ),
        GetPage(
          name: '/login',
          page: () => const LoginScreen(),
        ),
        GetPage(
          name: '/home',
          page: () => const MainNavigationScreen(),
          middlewares: [HomeAuthMiddleware()],
        ),
        GetPage(
          name: '/wallet',
          page: () => const WalletScreen(),
          middlewares: [WalletAuthMiddleware()],
        ),
        GetPage(
          name: '/create_post',
          page: () => const CreatePostScreen(),
          middlewares: [PostAuthMiddleware()],
        ),
        GetPage(
          name: '/search',
          page: () => const SearchScreen(),
          middlewares: [AuthMiddleware()],
        ),
        GetPage(
          name: '/edit_profile',
          page: () => const EditProfileScreen(),
          middlewares: [ProfileAuthMiddleware()],
        ),
        // Admin routes
        ...AdminRoutes.getPages(),
      ],
      routes: {
        '/landing': (context) => const LandingPage(),
        '/signup': (context) => const SignUpScreen(),
        '/login': (context) => const LoginScreen(),
        '/create-account': (context) => const CreateAccountScreen(),
        '/home': (context) => const MainNavigationScreen(),
        '/create_profile': (context) => const CreateProfileScreen(),
        '/category_selection': (context) => const CategorySelectionScreen(),
        '/choose_username': (context) => const ChooseUsernameScreen(),
        '/otp_verification': (context) =>
            const OtpVerificationScreen(email: ''),
        '/create_post': (context) => const CreatePostScreen(),
        '/post_feed': (context) => const PostFeedScreen(),
        '/categories_ranking': (context) => const CategoriesRankingScreen(),
        '/wallet': (context) => const WalletScreen(),
        '/search': (context) => const SearchScreen(),
        '/chats': (context) => const ChatListScreen(),
        '/edit_profile': (context) => const EditProfileScreen(),
        '/connect': (context) => ConnectScreen(),
        '/support': (context) => const SupportPage(),
        '/privacy': (context) => const PrivacyPolicyPage(),
        '/terms': (context) => const TermsOfServicePage(),
        '/contact': (context) => const ContactPage(),
        '/about': (context) => const AboutScreen(),
        // '/payment': (context) => const PaymentScreen(),
      },
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // Show loading while checking auth state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SplashScreen();
        }

        // If user is logged in and email verified
        if (snapshot.hasData && snapshot.data!.emailVerified) {
          return FutureBuilder<Map<String, dynamic>?>(
            future: _getUserDataAndInitializeProfile(snapshot.data!.uid),
            builder: (context, userSnapshot) {
              if (userSnapshot.connectionState == ConnectionState.waiting) {
                return const SplashScreen();
              }

              final userData = userSnapshot.data;
              final profileCompleted = userData?['profileCompleted'] ?? false;
              final hasUsername =
                  userData?['username']?.toString().isNotEmpty ?? false;
              final hasName = userData?['name']?.toString().isNotEmpty ?? false;
              // Email verification check removed since it's no longer required

              // Enhanced profile completion check with fallback logic (email verification not required)
              final isActuallyComplete =
                  profileCompleted || (hasUsername && hasName);

              if (isActuallyComplete) {
                return const MainNavigationScreen();
              } else if (!hasUsername) {
                return const ChooseUsernameScreen();
              } else {
                return const CreateProfileScreen();
              }
            },
          );
        }

        // User not logged in
        if (kIsWeb) {
          return const LandingPage();
        } else {
          return const SignUpScreen();
        }
      },
    );
  }

  Future<Map<String, dynamic>?> _getUserDataAndInitializeProfile(
    String uid,
  ) async {
    try {
      // Initialize ProfileController with current user
      await ProfileController.instance.initializeCurrentUser();

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(uid)
          .get()
          .timeout(const Duration(seconds: 20));
      return doc.data();
    } catch (e) {
      debugPrint('Error fetching user data and initializing profile: $e');
      return null;
    }
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthenticationState();
  }

  Future<void> _checkAuthenticationState() async {
    // Wait for Firebase to initialize and check auth state
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    final user = FirebaseAuth.instance.currentUser;

    if (user != null) {
      // Email verification check removed
      // User is logged in (email verification no longer required)
      try {
        // Initialize ProfileController with current user
        await ProfileController.instance.initializeCurrentUser();

        // Check if profile is completed
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get()
            .timeout(const Duration(seconds: 10));

        final userData = userDoc.data();
        final profileCompleted = userData?['profileCompleted'] ?? false;
        final hasUsername =
            userData?['username']?.toString().isNotEmpty ?? false;
        final hasName = userData?['name']?.toString().isNotEmpty ?? false;
        // Email verification check removed since it's no longer required

        // Enhanced profile completion check with fallback logic (email verification not required)
        final isActuallyComplete = profileCompleted || (hasUsername && hasName);

        if (!mounted) return;

        if (isActuallyComplete) {
          // Profile is complete, go to home
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const MainNavigationScreen(),
            ),
          );
        } else if (!hasUsername) {
          // Need to choose username
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const ChooseUsernameScreen(),
            ),
          );
        } else {
          // Need to complete profile
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const CreateProfileScreen(),
            ),
          );
        }
      } catch (e) {
        // Error checking profile, go to home anyway since user is authenticated
        debugPrint('Error checking profile completion: $e');
        if (!mounted) return;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
        );
      }
    } else {
      // User is not logged in
      if (kIsWeb) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LandingPage()),
        );
      } else {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const SignUpScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 150,
              height: 150,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
              child: ClipOval(
                child: Image.asset(
                  'assets/images/money_mouth.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 20),
            // App Name
            const Text(
              'Money Mouthy',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 5),
            // Tagline
            const Text(
              'Put Yo Money Where Yo Mouth is!',
              style: TextStyle(
                fontSize: 16,
                fontStyle: FontStyle.italic,
                color: Colors.black54,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Route handler for post detail deep links
class PostDetailRouteHandler extends StatefulWidget {
  final String postId;

  const PostDetailRouteHandler({super.key, required this.postId});

  @override
  State<PostDetailRouteHandler> createState() => _PostDetailRouteHandlerState();
}

class _PostDetailRouteHandlerState extends State<PostDetailRouteHandler> {
  final PostService _postService = PostService();
  bool _isLoading = true;
  Post? _post;
  String? _error;

  @override
  void initState() {
    super.initState();
    debugPrint('Loading post: ${widget.postId}');
    _loadPost();
  }

  Future<void> _loadPost() async {
    try {
      debugPrint('Loading post: ${widget.postId}');

      // Wait for app to be ready
      await _waitForAppReady();

      // Ensure PostService is initialized
      await _postService.initialize();

      // Find the post
      final posts = _postService.getAllPosts();
      final post = posts.firstWhereOrNull((p) => p.id == widget.postId);

      if (mounted) {
        setState(() {
          _post = post;
          _isLoading = false;
          if (post == null) {
            _error = 'Post not found';
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading post: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load post';
        });
      }
    }
  }

  Future<void> _waitForAppReady() async {
    // Wait for GetX to be ready
    int attempts = 0;
    while (!Get.isRegistered<PostController>() && attempts < 50) {
      await Future.delayed(const Duration(milliseconds: 100));
      attempts++;
    }
    debugPrint('PostDetailRouteHandler ready after $attempts attempts');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null || _post == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Post Not Found'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () =>
                Navigator.of(context).pushReplacementNamed('/home'),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                _error ?? 'Post not found',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () =>
                    Navigator.of(context).pushReplacementNamed('/home'),
                child: const Text('Go to Home'),
              ),
            ],
          ),
        ),
      );
    }

    return PostDetailScreen(post: _post!);
  }
}
