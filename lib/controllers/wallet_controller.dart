import 'dart:async';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/transaction_model.dart';
import '../services/payments/stripe_service.dart';
import '../services/transaction_manager.dart';

enum WalletStatus { uninitialized, initializing, ready, error, syncing }

class WalletController extends GetxController {
  static WalletController get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final TransactionManager _transactionManager = TransactionManager();

  // Reactive variables
  final _balance = 0.0.obs;
  final _transactions = <TransactionModel>[].obs;
  final _status = WalletStatus.uninitialized.obs;
  final _errorMessage = RxnString();
  final _isLoading = false.obs;
  final _totalEarnings = 0.0.obs;
  final _totalSpent = 0.0.obs;
  final _lastUpdated = DateTime.now().obs;

  // Stream subscriptions
  StreamSubscription<DocumentSnapshot>? _balanceSubscription;
  StreamSubscription<QuerySnapshot>? _transactionsSubscription;
  DocumentReference<Map<String, dynamic>>? _walletDoc;

  bool _isInitialized = false;
  Completer<void>? _initializationCompleter;

  // Smart caching and throttling
  Timer? _balanceUpdateTimer;
  Timer? _transactionUpdateTimer;
  static const Duration _updateThrottle = Duration(seconds: 2);
  DateTime? _lastBalanceUpdate;
  DateTime? _lastTransactionUpdate;

  // Getters
  double get balance => _balance.value;
  List<TransactionModel> get transactions => _transactions.toList();
  WalletStatus get status => _status.value;
  String? get errorMessage => _errorMessage.value;
  bool get isLoading => _isLoading.value;
  double get totalEarnings => _totalEarnings.value;
  double get totalSpent => _totalSpent.value;
  DateTime get lastUpdated => _lastUpdated.value;
  bool get isInitialized => _isInitialized;
  bool get isReady => _status.value == WalletStatus.ready;
  bool get hasError => _status.value == WalletStatus.error;

  // Computed properties
  List<TransactionModel> get creditTransactions =>
      _transactions.where((t) => t.isCredit).toList();

  List<TransactionModel> get debitTransactions =>
      _transactions.where((t) => t.isDebit).toList();

  List<TransactionModel> get pendingTransactions =>
      _transactions.where((t) => t.isPending).toList();

  @override
  void onInit() {
    super.onInit();
    // Auto-initialize when controller is created
    ever(_status, (WalletStatus status) {
      debugPrint('Wallet status changed to: $status');
    });
  }

  @override
  void onClose() {
    _balanceSubscription?.cancel();
    _transactionsSubscription?.cancel();
    _balanceUpdateTimer?.cancel();
    _transactionUpdateTimer?.cancel();
    super.onClose();
  }

  /// Dispose wallet data (for logout)
  void disposeWallet() {
    _balanceSubscription?.cancel();
    _transactionsSubscription?.cancel();
    _balanceUpdateTimer?.cancel();
    _transactionUpdateTimer?.cancel();

    // Reset state
    _balance.value = 0.0;
    _transactions.clear();
    _isInitialized = false;
    _updateStatus(WalletStatus.uninitialized);
    _errorMessage.value = null;
    _isLoading.value = false;

    debugPrint('WalletController: Disposed and reset for logout');
  }

  Future<void> initialize() async {
    if (_isInitialized) return;

    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    _initializationCompleter = Completer<void>();

    try {
      _updateStatus(WalletStatus.initializing);
      _isLoading.value = true;

      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Initialize transaction manager
      await _transactionManager.initialize(user.uid);

      _walletDoc = _firestore.collection('wallets').doc(user.uid);

      // Initialize wallet document if it doesn't exist
      await _initializeWalletDocument();

      // Set up real-time listeners
      await _setupListeners();

      _isInitialized = true;
      _updateStatus(WalletStatus.ready);
      _isLoading.value = false;

      _initializationCompleter!.complete();
    } catch (e) {
      _updateStatus(WalletStatus.error);
      _errorMessage.value = 'Failed to initialize wallet: $e';
      _isLoading.value = false;
      _initializationCompleter!.completeError(e);
      rethrow;
    }
  }

  Future<void> _initializeWalletDocument() async {
    if (_walletDoc == null) return;

    final docSnapshot = await _walletDoc!.get();
    if (!docSnapshot.exists) {
      // Create new wallet document with $2 welcome bonus
      const welcomeBonus = 2.0;

      // First create the wallet document with the welcome bonus balance
      await _walletDoc!.set({
        'balance': welcomeBonus,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Now create the welcome transaction record
      final welcomeTransaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.credit,
        amount: welcomeBonus,
        description: 'Welcome bonus - Thank you for joining Money Mouthy!',
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        metadata: {
          'type': 'welcome_bonus',
          'isSystemGenerated': true,
        },
      );

      // Add the transaction record (wallet already exists now)
      final transactionsCol = _walletDoc!.collection('transactions');
      await transactionsCol.add(welcomeTransaction.toMap());

      debugPrint(
          'New wallet created with \$${welcomeBonus.toStringAsFixed(2)} welcome bonus');
    } else {
      // Ensure balance field exists for existing wallets
      final data = docSnapshot.data();
      if (data == null || !data.containsKey('balance')) {
        await _walletDoc!.update({
          'balance': 0.0,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }
    }
  }

  Future<void> _setupListeners() async {
    if (_walletDoc == null) return;

    // Listen to balance changes with throttling
    _balanceSubscription = _walletDoc!.snapshots().listen(
      (snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          final balance = (data?['balance'] ?? 0.0).toDouble();

          // Throttle balance updates to prevent excessive UI rebuilds
          _balanceUpdateTimer?.cancel();
          _balanceUpdateTimer = Timer(_updateThrottle, () {
            if (_balance.value != balance) {
              _balance.value = balance;
              _lastUpdated.value = DateTime.now();
              _lastBalanceUpdate = DateTime.now();
            }
          });
        }
      },
      onError: (error) {
        debugPrint('Error listening to wallet balance: $error');
        _updateStatus(WalletStatus.error);
        _errorMessage.value = 'Failed to sync wallet balance: $error';
      },
    );

    // Listen to transaction changes with throttling and reduced limit
    _transactionsSubscription = _walletDoc!
        .collection('transactions')
        .orderBy('timestamp', descending: true)
        .limit(20) // Reduced from 100 to 20 for better performance
        .snapshots()
        .listen(
      (snapshot) {
        // Throttle transaction updates to prevent excessive processing
        _transactionUpdateTimer?.cancel();
        _transactionUpdateTimer = Timer(_updateThrottle, () {
          final transactions = snapshot.docs
              .map((doc) => TransactionModel.fromFirestore(doc))
              .toList();

          transactions.sort((t1, t2) => t2.timestamp.compareTo(t1.timestamp));
          _transactions.value = transactions;
          _calculateTotals(transactions);
          _lastUpdated.value = DateTime.now();
          _lastTransactionUpdate = DateTime.now();
        });
      },
      onError: (error) {
        debugPrint('Error listening to transactions: $error');
      },
    );
  }

  void _calculateTotals(List<TransactionModel> transactions) {
    final earnings = transactions
        .where((t) => t.isCredit && t.isCompleted)
        .fold(0.0, (total, t) => total + t.amount);

    final spent = transactions
        .where((t) => t.isDebit && t.isCompleted)
        .fold(0.0, (total, t) => total + t.amount);

    _totalEarnings.value = earnings;
    _totalSpent.value = spent;
  }

  void _updateStatus(WalletStatus newStatus) {
    _status.value = newStatus;
  }

  Future<bool> addFunds(double amount,
      {String? paymentMethodId, BuildContext? context}) async {
    if (!_isInitialized) {
      throw StateError('WalletController not initialized');
    }

    // Production-ready validation
    if (amount <= 0) {
      throw ArgumentError('Amount must be positive');
    }

    if (amount < 0.50) {
      throw ArgumentError('Minimum amount is \$0.50');
    }

    if (amount > 999999.00) {
      throw ArgumentError('Maximum amount is \$999,999.00');
    }

    _isLoading.value = true;

    try {
      // Process payment using Stripe Payment Sheet (mobile) or Modal (web)
      final paymentSuccess =
          await StripeService.processPayment(amount, context);

      if (!paymentSuccess) {
        _isLoading.value = false;
        return false;
      }

      // For mobile payments, we need to create the transaction locally
      // For web payments, the Firebase Function handles this
      if (!kIsWeb) {
        // Mobile: Create transaction record locally
        final transaction = TransactionModel(
          id: '', // Will be set by Firestore
          type: TransactionType.credit,
          amount: amount,
          description: 'Wallet funding via Stripe - ${formatCurrency(amount)}',
          timestamp: DateTime.now(),
          status: TransactionStatus.completed,
          paymentMethodId: paymentMethodId,
        );

        // Add transaction and update balance atomically
        await _transactionManager.addTransaction(
          transaction,
          _balance.value + amount,
        );
      }
      // For web: Transaction is created by Firebase Function (handlePaymentCompletion)
      // The wallet will be updated via real-time listeners

      _isLoading.value = false;
      return true;
    } catch (e) {
      debugPrint('Error adding funds: $e');
      _updateStatus(WalletStatus.error);

      // Simple error handling - the StripeService already provides user-friendly messages
      _errorMessage.value = e.toString();
      _isLoading.value = false;
      return false;
    }
  }

  Future<bool> deductFunds({
    required double amount,
    required String description,
    String? postId,
  }) async {
    if (!_isInitialized) {
      throw StateError('WalletController not initialized');
    }

    if (amount <= 0) {
      throw ArgumentError('Amount must be positive');
    }

    if (_balance.value < amount) {
      return false; // Insufficient funds
    }

    _isLoading.value = true;

    try {
      final transaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.debit,
        amount: amount,
        description: description,
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        postId: postId,
      );

      // Deduct from balance and add transaction atomically
      await _transactionManager.addTransaction(
        transaction,
        _balance.value - amount,
      );

      _isLoading.value = false;
      return true;
    } catch (e) {
      _updateStatus(WalletStatus.error);
      _errorMessage.value = 'Failed to deduct funds: $e';
      _isLoading.value = false;
      return false;
    }
  }

  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: '\$', decimalDigits: 2).format(amount);
  }

  /// Update a transaction's postId (useful for post creation)
  Future<void> updateTransactionPostId(
      String transactionId, String postId) async {
    if (!_isInitialized) {
      throw StateError('WalletController not initialized');
    }

    try {
      await _transactionManager.updateTransactionPostId(transactionId, postId);
    } catch (e) {
      debugPrint('Error updating transaction postId: $e');
      rethrow;
    }
  }

  /// Find the most recent transaction by description pattern
  Future<String?> findRecentTransactionByDescription(
      String descriptionPattern) async {
    if (!_isInitialized) {
      throw StateError('WalletController not initialized');
    }

    try {
      return await _transactionManager
          .findTransactionByDescription(descriptionPattern);
    } catch (e) {
      debugPrint('Error finding transaction by description: $e');
      return null;
    }
  }

  void clearError() {
    _updateStatus(WalletStatus.ready);
    _errorMessage.value = null;
  }

  /// Load more transactions for pagination
  Future<List<TransactionModel>> loadMoreTransactions({int limit = 20}) async {
    if (_walletDoc == null) return [];

    try {
      final lastTransaction =
          _transactions.isNotEmpty ? _transactions.last : null;
      Query<Map<String, dynamic>> query = _walletDoc!
          .collection('transactions')
          .orderBy('timestamp', descending: true)
          .limit(limit);

      if (lastTransaction != null) {
        // Get the document reference for pagination
        final lastDoc = await _walletDoc!
            .collection('transactions')
            .doc(lastTransaction.id)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => TransactionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error loading more transactions: $e');
      return [];
    }
  }

  // Refresh wallet data manually
  @override
  Future<void> refresh() async {
    if (_isInitialized) {
      await initialize();
    }
  }
}
