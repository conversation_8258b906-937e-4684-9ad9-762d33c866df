import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/admin_auth_controller.dart';
import '../theme/admin_theme.dart';
import 'admin_bottom_sheet.dart';

class AdminSidebar extends StatelessWidget {
  const AdminSidebar({super.key});

  @override
  Widget build(BuildContext context) {
    final adminAuthController = Get.find<AdminAuthController>();
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    if (isMobile) {
      return const SizedBox.shrink(); // Hide on mobile - use drawer instead
    }

    return Container(
      width: isTablet ? 240 : 280,
      decoration: BoxDecoration(
        color: AdminTheme.surfaceColor,
        boxShadow: AdminTheme.cardShadow,
      ),
      child: Column(
        children: [
          // Logo Section
          Container(
            height: 70,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.deepPurple,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.admin_panel_settings,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Money Mouthy',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Admin Panel',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Navigation Menu
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 16),
              children: [
                _buildMenuItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  route: '/admin/dashboard',
                  isSelected: Get.currentRoute == '/admin/dashboard',
                ),
                Obx(() => _buildMenuItem(
                      icon: Icons.people,
                      title: 'User Management',
                      route: '/admin/users',
                      isSelected: Get.currentRoute == '/admin/users',
                      enabled: adminAuthController.canManageUsers,
                    )),
                Obx(() => _buildMenuItem(
                      icon: Icons.account_balance_wallet,
                      title: 'Wallet Management',
                      route: '/admin/wallets',
                      isSelected: Get.currentRoute == '/admin/wallets',
                      enabled: adminAuthController.canManageWallets,
                    )),
                _buildMenuItem(
                  icon: Icons.timeline,
                  title: 'User Activity',
                  route: '/admin/activity',
                  isSelected: Get.currentRoute == '/admin/activity',
                ),
              ],
            ),
          ),

          // Bottom Section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Column(
              children: [
                Obx(() => ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.deepPurple,
                        child: Text(
                          adminAuthController.currentAdmin?.name
                                  .substring(0, 1)
                                  .toUpperCase() ??
                              'A',
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                      title: Text(
                        adminAuthController.currentAdmin?.name ?? 'Admin',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      subtitle: Text(
                        adminAuthController.currentAdmin?.role.name
                                .toUpperCase() ??
                            '',
                        style: const TextStyle(fontSize: 12),
                      ),
                      contentPadding: EdgeInsets.zero,
                    )),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _showLogoutDialog(context);
                    },
                    icon: const Icon(Icons.logout, size: 18),
                    label: const Text('Logout'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String route,
    bool isSelected = false,
    bool enabled = true,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: ListTile(
        leading: Icon(
          icon,
          color: enabled
              ? (isSelected ? Colors.deepPurple : Colors.grey[600])
              : Colors.grey[400],
        ),
        title: Text(
          title,
          style: TextStyle(
            color: enabled
                ? (isSelected ? Colors.deepPurple : Colors.grey[800])
                : Colors.grey[400],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: Colors.deepPurple.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: enabled
            ? () {
                if (!isSelected) {
                  Get.toNamed(route);
                }
              }
            : null,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    ConfirmationBottomSheet.show(
      context: context,
      title: 'Confirm Logout',
      message: 'Are you sure you want to logout from the admin panel?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
      confirmColor: Colors.red,
      icon: Icons.logout,
      onConfirm: () {
        Get.find<AdminAuthController>().signOut();
        Get.offAllNamed('/admin/login');
      },
    );
  }
}
