import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../controllers/category_controller.dart';
import '../controllers/post_controller.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/draft_controller.dart';
import '../controllers/poll_controller.dart';
import 'user_cache_service.dart';
import 'user_service.dart';

/// Comprehensive logout service that handles all data clearing and proper logout
class LogoutService {
  static final LogoutService _instance = LogoutService._internal();
  factory LogoutService() => _instance;
  LogoutService._internal();

  /// Perform complete logout with data clearing
  static Future<bool> performLogout() async {
    try {
      debugPrint('LogoutService: Starting comprehensive logout process');

      // 1. Clear all SharedPreferences data
      await _clearSharedPreferences();

      // 2. Clear all GetX controllers and their cached data
      await _clearGetXControllers();

      // 3. Clear all service caches
      await _clearServiceCaches();

      // 4. Sign out from Firebase Auth
      await _signOutFromFirebase();

      debugPrint('LogoutService: Logout completed successfully');
      return true;
    } catch (e) {
      debugPrint('LogoutService: Error during logout: $e');
      return false;
    }
  }

  /// Clear all SharedPreferences data
  static Future<void> _clearSharedPreferences() async {
    try {
      debugPrint('LogoutService: Clearing SharedPreferences');
      final prefs = await SharedPreferences.getInstance();

      // Get all keys to clear
      final keys = prefs.getKeys();
      debugPrint(
          'LogoutService: Found ${keys.length} SharedPreferences keys to clear');

      // Clear all keys
      for (final key in keys) {
        await prefs.remove(key);
      }

      // Alternative: Clear all at once (more efficient)
      // await prefs.clear();

      debugPrint('LogoutService: SharedPreferences cleared successfully');
    } catch (e) {
      debugPrint('LogoutService: Error clearing SharedPreferences: $e');
      rethrow;
    }
  }

  /// Clear all GetX controllers and their cached data
  static Future<void> _clearGetXControllers() async {
    try {
      debugPrint('LogoutService: Clearing GetX controllers');

      // Clear ProfileController data
      if (Get.isRegistered<ProfileController>()) {
        final profileController = Get.find<ProfileController>();
        profileController.clearAllCache();
        debugPrint('LogoutService: ProfileController cache cleared');
      }

      // Clear UserCacheService data
      if (Get.isRegistered<UserCacheService>()) {
        final userCacheService = Get.find<UserCacheService>();
        userCacheService.clearCache();
        debugPrint('LogoutService: UserCacheService cache cleared');
      }

      // Clear PostController data
      if (Get.isRegistered<PostController>()) {
        final postController = Get.find<PostController>();
        postController.clearCache();
        debugPrint('LogoutService: PostController cache cleared');
      }

      // Clear CategoryController data
      if (Get.isRegistered<CategoryController>()) {
        final categoryController = Get.find<CategoryController>();
        categoryController.clearCache();
        debugPrint('LogoutService: CategoryController cache cleared');
      }

      // Clear WalletController data
      if (Get.isRegistered<WalletController>()) {
        final walletController = Get.find<WalletController>();
        walletController.disposeWallet();
        debugPrint('LogoutService: WalletController disposed');
      }

      // Clear DraftController data
      if (Get.isRegistered<DraftController>()) {
        final draftController = Get.find<DraftController>();
        draftController.clearAllDrafts();
        debugPrint('LogoutService: DraftController drafts cleared');
      }

      // Clear PollController data
      if (Get.isRegistered<PollController>()) {
        final pollController = Get.find<PollController>();
        pollController.clearAllPollData();
        debugPrint('LogoutService: PollController data cleared');
      }

      // Delete non-essential controllers (they will be recreated on next login)
      _deleteNonEssentialControllers();

      debugPrint('LogoutService: GetX controllers cleared successfully');
    } catch (e) {
      debugPrint('LogoutService: Error clearing GetX controllers: $e');
      rethrow;
    }
  }

  /// Delete non-essential controllers that should be recreated on login
  static void _deleteNonEssentialControllers() {
    try {
      // Keep only essential controllers (ProfileController and UserCacheService)
      // Delete others so they can be recreated fresh on next login

      if (Get.isRegistered<CategoryController>()) {
        Get.delete<CategoryController>();
        debugPrint('LogoutService: CategoryController deleted');
      }

      if (Get.isRegistered<PostController>()) {
        Get.delete<PostController>();
        debugPrint('LogoutService: PostController deleted');
      }

      if (Get.isRegistered<WalletController>()) {
        Get.delete<WalletController>();
        debugPrint('LogoutService: WalletController deleted');
      }

      if (Get.isRegistered<DraftController>()) {
        Get.delete<DraftController>();
        debugPrint('LogoutService: DraftController deleted');
      }

      if (Get.isRegistered<PollController>()) {
        Get.delete<PollController>();
        debugPrint('LogoutService: PollController deleted');
      }
    } catch (e) {
      debugPrint('LogoutService: Error deleting controllers: $e');
    }
  }

  /// Clear all service caches
  static Future<void> _clearServiceCaches() async {
    try {
      debugPrint('LogoutService: Clearing service caches');

      // Clear UserService cache
      UserService().clearAllCache();
      debugPrint('LogoutService: UserService cache cleared');

      debugPrint('LogoutService: Service caches cleared successfully');
    } catch (e) {
      debugPrint('LogoutService: Error clearing service caches: $e');
      rethrow;
    }
  }

  /// Sign out from Firebase Auth
  static Future<void> _signOutFromFirebase() async {
    try {
      debugPrint('LogoutService: Signing out from Firebase Auth');
      await FirebaseAuth.instance.signOut();
      debugPrint('LogoutService: Firebase Auth sign out completed');
    } catch (e) {
      debugPrint('LogoutService: Error signing out from Firebase: $e');
      rethrow;
    }
  }

  /// Check if user is authenticated
  static bool isUserAuthenticated() {
    final user = FirebaseAuth.instance.currentUser;
    return user != null;
  }

  /// Get current user ID (null if not authenticated)
  static String? getCurrentUserId() {
    return FirebaseAuth.instance.currentUser?.uid;
  }
}
