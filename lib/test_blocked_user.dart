// This is a test file to demonstrate blocked user functionality
// This file can be deleted after testing

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class TestBlockedUserScreen extends StatefulWidget {
  const TestBlockedUserScreen({super.key});

  @override
  State<TestBlockedUserScreen> createState() => _TestBlockedUserScreenState();
}

class _TestBlockedUserScreenState extends State<TestBlockedUserScreen> {
  bool _isLoading = false;
  String? _message;

  Future<void> _blockCurrentUser() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      setState(() {
        _message = 'No user logged in';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _message = null;
    });

    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .update({
        'status': 'blocked',
        'blockReason': 'Test block for demonstration',
        'blockedAt': FieldValue.serverTimestamp(),
      });

      setState(() {
        _message = 'User blocked successfully. Try navigating or refreshing the app.';
      });
    } catch (e) {
      setState(() {
        _message = 'Error blocking user: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _unblockCurrentUser() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      setState(() {
        _message = 'No user logged in';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _message = null;
    });

    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .update({
        'status': 'active',
        'blockReason': FieldValue.delete(),
        'blockedAt': FieldValue.delete(),
      });

      setState(() {
        _message = 'User unblocked successfully.';
      });
    } catch (e) {
      setState(() {
        _message = 'Error unblocking user: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Blocked User'),
        backgroundColor: const Color(0xFF0f172a),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Blocked User Functionality',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This screen allows you to test the blocked user functionality by temporarily blocking/unblocking the current user.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isLoading ? null : _blockCurrentUser,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('Block Current User'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _unblockCurrentUser,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('Unblock Current User'),
            ),
            const SizedBox(height: 32),
            if (_message != null)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _message!,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            const SizedBox(height: 32),
            const Text(
              'Instructions:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Click "Block Current User" to block yourself\n'
              '2. Try navigating to different screens or refreshing\n'
              '3. You should see the blocked user dialog\n'
              '4. Use "Unblock Current User" to restore access\n\n'
              'Note: The blocking takes effect immediately due to real-time monitoring.',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
